import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';

/// A highly optimized and memory-efficient notifier for managing ArtistData using Map.
///
/// Uses `Map<int, ArtistData>` for O(1) operations and eliminates the need for index caching.
final class ArtistStoreNotifier extends Notifier<Map<int, ArtistData>> {
  @override
  Map<int, ArtistData> build() {
    return {};
  }

  void _updateState(Map<int, ArtistData> newState) {
    // Only update if the map reference actually changed
    if (!mapEquals(state, newState)) {
      state = newState;
    }
  }

  ArtistData? getItem(int id) {
    return state[id];
  }

  List<ArtistData> getItems(List<int> ids) {
    if (ids.isEmpty) return const [];

    // Efficiently collect multiple artist items - O(k) where k is ids.length
    final result = <ArtistData>[];
    for (final id in ids) {
      final artist = state[id];
      if (artist != null) {
        result.add(artist);
      }
    }
    return result;
  }

  int getIndex(int id) {
    // Index concept doesn't apply to Map, but we can return a consistent value
    // for backwards compatibility. Return -1 if not found, otherwise a hash-based index
    return state.containsKey(id) ? id.hashCode.abs() % 1000000 : -1;
  }

  bool hasItem(int id) {
    return state.containsKey(id);
  }

  /// Adds a new artist only if it doesn't already exist - O(1)
  void addItem(ArtistData newItem, {bool updateIfExists = true}) {
    if (state.containsKey(newItem.id)) {
      if (updateIfExists) updateItem(newItem);
      return;
    }

    final newState = Map<int, ArtistData>.from(state);
    newState[newItem.id] = newItem;
    _updateState(newState);
  }

  /// Adds a list of artists uniquely, optionally updating existing ones - O(k)
  void addItems(List<ArtistData> newItems, {bool updateIfExists = true}) {
    if (newItems.isEmpty) return;

    final newState = Map<int, ArtistData>.from(state);
    bool hasChanges = false;

    for (final item in newItems) {
      final exists = newState.containsKey(item.id);

      if (!exists) {
        newState[item.id] = item;
        hasChanges = true;
      } else if (updateIfExists && newState[item.id] != item) {
        newState[item.id] = item;
        hasChanges = true;
      }
    }

    if (hasChanges) {
      _updateState(newState);
    }
  }

  /// Updates a single existing artist.
  ///
  /// If [addIfNotExists] is true, will add the photo if it doesn't exist.
  void updateItem(ArtistData newItem, {bool addIfNotExists = true}) {
    if (!state.containsKey(newItem.id)) {
      if (addIfNotExists) {
        addItem(newItem, updateIfExists: false);
      }
      return;
    }

    if (state[newItem.id] == newItem) return;

    final newState = Map<int, ArtistData>.from(state);
    newState[newItem.id] = newItem;
    _updateState(newState);
  }

  /// Efficiently updates a batch of existing items - O(k)
  void updateItems(List<ArtistData> newItems, {bool addIfNotExists = true}) {
    if (newItems.isEmpty) return;

    final newState = Map<int, ArtistData>.from(state);
    bool hasChanges = false;

    for (final item in newItems) {
      final exists = newState.containsKey(item.id);

      if (exists) {
        if (newState[item.id] != item) {
          newState[item.id] = item;
          hasChanges = true;
        }
      } else if (addIfNotExists) {
        newState[item.id] = item;
        hasChanges = true;
      }
    }

    if (hasChanges) {
      _updateState(newState);
    }
  }

  /// Removes an artist by ID - O(1)
  void removeItem(int id) {
    if (!state.containsKey(id)) return;

    final newState = Map<int, ArtistData>.from(state);
    newState.remove(id);
    _updateState(newState);
  }

  /// Removes multiple artists by their IDs - O(k)
  void removeItems(List<int> ids) {
    if (ids.isEmpty) return;

    final newState = Map<int, ArtistData>.from(state);
    bool hasChanges = false;

    for (final id in ids) {
      if (newState.remove(id) != null) {
        hasChanges = true;
      }
    }

    if (hasChanges) {
      _updateState(newState);
    }
  }

  /// Replaces the entire store with a new list of artists - O(k)
  void replaceAll(List<ArtistData> newList) {
    final newState = <int, ArtistData>{};
    for (final artist in newList) {
      newState[artist.id] = artist;
    }
    _updateState(newState);
  }

  /// Clears all artists in the store - O(1)
  void clear() {
    if (state.isNotEmpty) {
      _updateState(<int, ArtistData>{});
    }
  }

  // ------------------------------------------------------------
  // Specialized update methods for single artist fields - O(1)
  // ------------------------------------------------------------

  void _updateArtistField(
    int artistId,
    ArtistData Function(ArtistData) updater,
  ) {
    final currentArtist = state[artistId];
    if (currentArtist == null) return;

    final updatedArtist = updater(currentArtist);

    if (currentArtist != updatedArtist) {
      final newState = Map<int, ArtistData>.from(state);
      newState[artistId] = updatedArtist;
      _updateState(newState);
    }
  }

  void setIsFollowing(int artistId, bool isFollowing) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(isFollowing: isFollowing),
    );
  }

  void setTotalFollowing(int artistId, int totalFollowing) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(totalFollowing: totalFollowing),
    );
  }

  void addTotalFollowing(int artistId, int amount) {
    if (amount == 0) return;

    _updateArtistField(
      artistId,
      (artist) =>
          artist.copyWith(totalFollowing: artist.totalFollowing + amount),
    );
  }

  void incrementTotalFollowing(int artistId) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(totalFollowing: artist.totalFollowing + 1),
    );
  }

  void decrementTotalFollowing(int artistId) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(
        totalFollowing: (artist.totalFollowing - 1)
            .clamp(0, double.infinity)
            .toInt(),
      ),
    );
  }

  void setTotalFollowers(int artistId, int totalFollowers) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(totalFollowers: totalFollowers),
    );
  }

  void incrementTotalFollowers(int artistId) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(totalFollowers: artist.totalFollowers + 1),
    );
  }

  void decrementTotalFollowers(int artistId) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(
        totalFollowers: (artist.totalFollowers - 1)
            .clamp(0, double.infinity)
            .toInt(),
      ),
    );
  }

  void setIsBlocked(int artistId, bool isBlocked) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(isBlocked: isBlocked),
    );
  }

  void setIsBlocking(int artistId, bool isBlocking) {
    _updateArtistField(
      artistId,
      (artist) => artist.copyWith(isBlocking: isBlocking),
    );
  }

  // ------------------------------------------------------------
  // Utility getters for common use cases
  // ------------------------------------------------------------

  /// Returns total number of artists - O(1)
  int get count => state.length;

  /// Checks if the store is empty - O(1)
  bool get isEmpty => state.isEmpty;

  /// Checks if the store has at least one artist - O(1)
  bool get isNotEmpty => state.isNotEmpty;

  /// Returns all artist IDs - O(n)
  List<int> get allIds => state.keys.toList(growable: false);

  /// Returns all artists - O(n)
  List<ArtistData> get allArtists => state.values.toList(growable: false);

  /// Returns all artists by multiple IDs - O(k)
  List<ArtistData> getArtistsByIds(List<int> ids) {
    return getItems(ids);
  }
}

/// NotifierProvider for the main artist store
final artistStoreProvider =
    NotifierProvider.autoDispose<ArtistStoreNotifier, Map<int, ArtistData>>(
      ArtistStoreNotifier.new,
    );

/// A selective provider that only rebuilds when a specific artist field changes
final artistProvider =
    Provider.family<
      ({
        bool? isFollowing,
        int? totalFollowing,
        int? totalFollowers,
        bool? isBlocked,
        bool? isBlocking,
      }),
      int
    >((ref, artistId) {
      final store = ref.watch(artistStoreProvider.notifier);
      final artist = store.getItem(artistId);

      return (
        isFollowing: artist?.isFollowing,
        totalFollowing: artist?.totalFollowing,
        totalFollowers: artist?.totalFollowers,
        isBlocked: artist?.isBlocked,
        isBlocking: artist?.isBlocking,
      );
    });

/// Provider that returns the current total artist count
final artistCountProvider = Provider.autoDispose<int>((ref) {
  return ref.watch(artistStoreProvider.select((artists) => artists.length));
});

/// Provider that checks if the store has any artists
final hasArtistsProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(artistStoreProvider.select((artists) => artists.isNotEmpty));
});

/// Provider that checks if a specific artist ID exists
final hasArtistProvider = Provider.family.autoDispose<bool, int>((
  ref,
  artistId,
) {
  return ref.watch(artistStoreProvider.notifier).hasItem(artistId);
});

/// Provider that returns a specific artist by ID
final specificArtistProvider = Provider.family.autoDispose<ArtistData?, int>((
  ref,
  artistId,
) {
  return ref.watch(artistStoreProvider.notifier).getItem(artistId);
});

/// Provider that returns all artists as a List for UI components that need it
final artistListProvider = Provider.autoDispose<List<ArtistData>>((ref) {
  return ref.watch(
    artistStoreProvider.select((artists) => artists.values.toList()),
  );
});
