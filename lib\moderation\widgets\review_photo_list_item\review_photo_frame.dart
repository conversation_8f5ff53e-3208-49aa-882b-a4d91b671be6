import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/like_photo_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/like_photo_service.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_image.dart';

class ReviewPhotoFrame extends ConsumerStatefulWidget {
  const ReviewPhotoFrame({
    super.key,
    this.containerWidth,
    required this.photo,
    this.isOwnPhoto = false,
    required this.screenName,
    this.isSmall = false,
    this.showMetadata = true,
    this.isProcessing = true,
    this.zoomable = true,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onTap,
    this.onLongPress,
  });

  final double? containerWidth;
  final PhotoData photo;
  final bool isOwnPhoto;
  final String screenName;
  final bool isSmall;
  final bool showMetadata;
  final bool isProcessing;
  final bool zoomable;
  final Function? onTwoFingersOn;
  final Function? onTwoFingersOff;
  final Function? onTap;
  final Function? onLongPress;

  @override
  ReviewPhotoFrameState createState() => ReviewPhotoFrameState();
}

class ReviewPhotoFrameState extends ConsumerState<ReviewPhotoFrame>
    with SingleTickerProviderStateMixin {
  final _likePhotoService = LikePhotoService();

  late double _heartIconSize = 0.0;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.5).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double wrapperWidth = widget.containerWidth ?? constraints.maxWidth;

        double photoHeight = widget.photo.width != 0 && widget.photo.height != 0
            ? ImageSize.computedHeight(
                parentWidth: wrapperWidth,
                imageWidth: widget.photo.width.toDouble(),
                imageHeight: widget.photo.height.toDouble(),
              )
            : 350.0;

        return SizedBox(
          height: photoHeight,
          child: Stack(
            fit: StackFit.loose,
            children: [
              PhotoImage(
                photoUrl: widget.photo.url,
                height: photoHeight,
                zoomable: widget.zoomable,
                onTwoFingersOn: widget.zoomable ? widget.onTwoFingersOn : null,
                onTwoFingersOff: widget.zoomable
                    ? widget.onTwoFingersOff
                    : null,
                onLongPress: () => _handlePhotoLongPress(context),
                onTap: () => _handlePhotoTap(context),
                onDoubleTap: _handlePhotoDoubleTap,
              ),
              Center(
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: FadeTransition(
                    opacity: _opacityAnimation,
                    child: Icon(
                      Ionicons.heart,
                      color: context.colors.accentColor,
                      size: _heartIconSize,
                    ),
                  ),
                ),
              ),
              if (widget.showMetadata)
                Positioned(
                  bottom: widget.isSmall ? 40.0 : 55.0,
                  left: widget.isSmall ? 10.0 : 15.0,
                  child: Text(
                    (widget.photo.aperture.isNotEmpty &&
                            widget.photo.aperture != '0'
                        ? 'F/${widget.photo.aperture}'
                        : ''),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: widget.isSmall ? 10.0 : 16.0,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              if (widget.showMetadata)
                Positioned(
                  bottom: widget.isSmall ? 25.0 : 35.0,
                  left: widget.isSmall ? 10.0 : 15.0,
                  child: Text(
                    widget.photo.shutterSpeed,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: widget.isSmall ? 10.0 : 16.0,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              if (widget.showMetadata)
                Positioned(
                  bottom: widget.isSmall ? 10.0 : 15.0,
                  left: widget.isSmall ? 10.0 : 15.0,
                  child: Text(
                    (widget.photo.iso.isNotEmpty && widget.photo.iso != '0'
                        ? 'ISO ${widget.photo.iso}'
                        : ''),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: widget.isSmall ? 10.0 : 16.0,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              if (widget.isProcessing)
                Positioned(
                  top: 0.0,
                  left: 0.0,
                  child: Container(
                    width: wrapperWidth,
                    height: photoHeight,
                    color: const Color.fromRGBO(0, 0, 0, 0.3),
                    child: const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  void _handlePhotoTap(BuildContext context) {
    if (widget.onTap != null) {
      widget.onTap!();
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoDetailScreen(
          photo: widget.photo,
          originScreenName: widget.screenName,
        ),
      ),
    );
  }

  /// We do only *like* (no *unlike*) on double tap.
  /// This is what Instagram does as well.
  ///
  /// We do this to avoid the risk when the user frequently double taps.
  ///
  /// We could toggle like and unlike on double tap.
  /// But what if they double tap multiple times in a row?
  void _handlePhotoDoubleTap() {
    setState(() {
      _heartIconSize = widget.isSmall ? 70.0 : 100.0;
    });

    _likePhoto();
    _animateLikeAction();

    // if (widget.photo.isLiked) {
    //   _animateUnlikeAction(context);
    //   _likePhoto();
    // } else {
    //   _animateLikeAction(context);
    //   _unlikePhoto();
    // }
  }

  void _animateLikeAction() {
    _animationController.reset();
    _animationController.forward().whenComplete(() {
      setState(() {
        _heartIconSize = 0.0;
      });
    });
  }

  Future<void> _likePhoto() async {
    final bool isLiked =
        ref.read(photoProvider(widget.photo.id))?.isLiked ??
        widget.photo.isLiked;

    // Only run if the user hasn't liked this photo.
    if (isLiked) return;

    // Set the `isLiked` to true state even though the request hasn't been made.
    ref.read(photoStoreProvider.notifier).setIsLiked(widget.photo.id, true);

    LikePhotoResponse response = await _likePhotoService.like(widget.photo.id);

    if (!response.success || response.data == null) {
      // Switch the `isLiked` state back to false if the status is failed.
      ref.read(photoStoreProvider.notifier).setIsLiked(widget.photo.id, false);

      return;
    }

    ref
        .read(photoStoreProvider.notifier)
        .setTotalLikes(widget.photo.id, response.data!.totalLikes);
  }

  void _handlePhotoLongPress(BuildContext context) {
    if (widget.onTwoFingersOff != null) {
      widget.onTwoFingersOff!();
    }

    if (widget.onLongPress != null) {
      widget.onTwoFingersOff!();
    }

    if (widget.onLongPress != null) {
      widget.onLongPress!();
    }
  }
}
