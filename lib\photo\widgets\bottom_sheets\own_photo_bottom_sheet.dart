import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/album/widgets/album_assignment_modal.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/widgets/edit_photo_modal.dart';

class OwnPhotoBottomSheet extends ConsumerStatefulWidget {
  final PhotoData photo;
  final String screenName;
  final AssignmentActionType? archiveAssignmentType;
  final bool showExtraMenu;
  final Function(AssignmentActionType, PhotoData)? onPhotoArchiveAssignment;
  final Function(PhotoData)? onRequestFeedback;
  final Function(PhotoData)? onDeletePhoto;
  final void Function({
    required AssignmentActionType action,
    bool notifyAuthor,
  })?
  onFeaturedAssignment;
  final void Function({required AssignmentActionType action})? onPotdAssignment;

  const OwnPhotoBottomSheet({
    super.key,
    required this.photo,
    required this.screenName,
    this.archiveAssignmentType,
    this.showExtraMenu = true,
    this.onPhotoArchiveAssignment,
    this.onRequestFeedback,
    this.onDeletePhoto,
    this.onFeaturedAssignment,
    this.onPotdAssignment,
  });

  @override
  OwnPhotoModalState createState() => OwnPhotoModalState();
}

class OwnPhotoModalState extends ConsumerState<OwnPhotoBottomSheet> {
  late final String _role;

  @override
  void initState() {
    super.initState();
    _role = LocalUserService.role ?? 'subscriber';
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> menuItems = [];

    final bool isFeatured =
        ref.watch(photoProvider(widget.photo.id))?.featured ??
        widget.photo.featured;

    final bool isPotd =
        ref.watch(photoProvider(widget.photo.id))?.potd ?? widget.photo.potd;

    if (widget.screenName == 'my_profile' ||
        widget.screenName == 'archived_photos_screen') {
      if (widget.showExtraMenu) {
        menuItems.addAll([
          ModalListTile(
            title: "Edit photo",
            iconData: Ionicons.create_outline,
            onTap: () {
              Navigator.pop(context);
              _showEditPhotoModal();
            },
          ),
          ModalListTile(
            title: "Assign to album",
            iconData: Ionicons.folder_outline,
            onTap: () {
              Navigator.pop(context);
              _showAlbumAssignmentModal();
            },
          ),
        ]);

        if (widget.screenName == 'my_profile') {
          if (widget.photo.needsFeedback) {
            menuItems.add(
              ModalListTile(
                title: "Feedback requested",
                svgAssetPath: 'assets/feedback_icon.svg',
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            );
          } else {
            menuItems.add(
              ModalListTile(
                title: "Request feedback",
                svgAssetPath: 'assets/feedback_icon.svg',
                onTap: () {
                  Navigator.pop(context);
                  widget.onRequestFeedback?.call(widget.photo);
                },
              ),
            );
          }
        }
      }
    }

    menuItems.addAll([
      ModalListTile(
        title: widget.archiveAssignmentType == null
            ? (widget.screenName == 'archived_photos_screen'
                  ? 'Unarchive photo'
                  : 'Archive photo')
            : (widget.archiveAssignmentType == AssignmentActionType.assign
                  ? 'Archive photo'
                  : 'Unarchive photo'),
        iconData: Ionicons.archive_outline,
        onTap: () {
          Navigator.pop(context);

          if (widget.onPhotoArchiveAssignment != null) {
            final AssignmentActionType actionType =
                widget.archiveAssignmentType == null
                ? widget.screenName == 'archived_photos_screen'
                      ? AssignmentActionType.unassign
                      : AssignmentActionType.assign
                : widget.archiveAssignmentType!;

            widget.onPhotoArchiveAssignment!(actionType, widget.photo);
          }
        },
      ),
      ModalListTile(
        title: 'Delete photo',
        iconData: Ionicons.archive_outline,
        textColor: context.colors.dangerColor,
        iconColor: context.colors.dangerColor,
        onTap: () {
          Navigator.pop(context);
          _showDeletePhotoDialog();
        },
      ),
    ]);

    if (_role == 'administrator' || _role == 'editor') {
      if (isFeatured) {
        menuItems.add(
          ModalListTile(
            title: "Remove from featured",
            iconData: Ionicons.trophy_outline,
            onTap: () {
              Navigator.pop(context);
              widget.onFeaturedAssignment?.call(
                action: AssignmentActionType.unassign,
              );
            },
          ),
        );

        if (widget.screenName == 'featured_screen') {
          if (isPotd) {
            menuItems.add(
              ModalListTile(
                title: "Unset from potd",
                iconData: Ionicons.trophy_outline,
                onTap: () {
                  Navigator.pop(context);
                  widget.onPotdAssignment?.call(
                    action: AssignmentActionType.unassign,
                  );
                },
              ),
            );
          } else {
            menuItems.add(
              ModalListTile(
                title: "Set as potd",
                textColor: context.colors.successColor,
                iconColor: context.colors.successColor,
                iconData: Ionicons.trophy_outline,
                onTap: () {
                  Navigator.pop(context);
                  widget.onPotdAssignment?.call(
                    action: AssignmentActionType.assign,
                  );
                },
              ),
            );
          }
        }
      } else {
        menuItems.add(
          ModalListTile(
            title: "Set as featured without notification",
            iconData: Ionicons.trophy_outline,
            onTap: () {
              Navigator.pop(context);

              if (widget.onFeaturedAssignment != null) {
                widget.onFeaturedAssignment!(
                  action: AssignmentActionType.assign,
                  notifyAuthor: false,
                );
              }
            },
          ),
        );
      }
    }

    return Container(
      padding: const EdgeInsets.only(
        top: 9.0,
        // bottom: MediaQuery.viewInsetsOf(context).bottom,
      ),
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          height:
              BottomSheetConfig.extraSpace +
              (BottomSheetConfig.menuItemHeight * menuItems.length),
          child: Column(children: menuItems),
        ),
      ),
    );
  }

  void _showEditPhotoModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          maxChildSize: 0.9,
          initialChildSize: 0.45,
          expand: false,
          builder: ((context, scrollController) {
            return Container(
              padding: EdgeInsets.only(
                top: 8.0,
                bottom: MediaQuery.viewInsetsOf(context).bottom + 15.0,
              ),
              decoration: BoxDecoration(
                color: context.colors.scaffoldColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20.0),
                  topRight: Radius.circular(20.0),
                ),
              ),
              child: SafeArea(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: EditPhotoModal(photo: widget.photo),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  void _showAlbumAssignmentModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          maxChildSize: 0.9,
          expand: false,
          builder: ((context, scrollController) {
            return Container(
              padding: EdgeInsets.only(
                top: 8.0,
                bottom: MediaQuery.viewInsetsOf(context).bottom,
              ),
              decoration: BoxDecoration(
                color: context.colors.scaffoldColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20.0),
                  topRight: Radius.circular(20.0),
                ),
              ),
              child: AlbumAssignmentModal(
                scrollController: scrollController,
                photo: widget.photo,
                onSaved: _handleOnAlbumSaved,
              ),
            );
          }),
        );
      },
    );
  }

  void _handleOnAlbumSaved(String msg) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(duration: const Duration(seconds: 2), content: Text(msg)),
    );
  }

  void _showDeletePhotoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Delete photo'),
        content: const Text('Are you sure you want to delete this photo?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDeletePhoto?.call(widget.photo);
            },
            child: Text(
              'Delete',
              style: TextStyle(color: context.colors.dangerColor),
            ),
          ),
        ],
      ),
    );
  }
}
