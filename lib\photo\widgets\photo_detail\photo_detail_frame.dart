import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/like_photo_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/like_photo_service.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_image.dart';
import 'package:portraitmode/photo/widgets/photo_viewer_screen.dart';

class PhotoDetailFrame extends ConsumerStatefulWidget {
  const PhotoDetailFrame({
    super.key,
    required this.photo,
    this.isProcessing = false,
    this.metaDataFontSize = 16.0,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
  });

  final PhotoData photo;
  final double metaDataFontSize;
  final Function? onTwoFingersOn;
  final Function? onTwoFingersOff;
  final bool isProcessing;

  @override
  PhotoDetailFrameState createState() => PhotoDetailFrameState();
}

class PhotoDetailFrameState extends ConsumerState<PhotoDetailFrame>
    with SingleTickerProviderStateMixin {
  double photoHeight = 350.0;
  final _likePhotoService = LikePhotoService();

  late double _heartIconSize = 0.0;

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.5).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.sizeOf(context).width;

    if (mounted && widget.photo.width != 0 && widget.photo.height != 0) {
      photoHeight = ImageSize.computedHeight(
        parentWidth: screenWidth,
        imageWidth: widget.photo.width.toDouble(),
        imageHeight: widget.photo.height.toDouble(),
      );
    }

    return SizedBox(
      height: photoHeight,
      child: Stack(
        alignment: AlignmentDirectional.topStart,
        fit: StackFit.loose,
        children: [
          PhotoDetailImage(
            photoUrl: widget.photo.url,
            photoUrlLarge: widget.photo.urlLarge,
            height: photoHeight,
            zoomable: true,
            onTwoFingersOn: widget.onTwoFingersOn,
            onTwoFingersOff: widget.onTwoFingersOff,
            onTap: _handleOnPhotoTap,
            onDoubleTap: _handleOnPhotoDoubleTap,
          ),
          Center(
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: FadeTransition(
                opacity: _opacityAnimation,
                child: Icon(
                  Ionicons.heart,
                  color: context.colors.accentColor,
                  size: _heartIconSize,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0.0,
            left: 0.0,
            child: IconButton(
              onPressed: _handleOnPhotoTap,
              icon: const Icon(
                Ionicons.scan_outline,
                size: 20.0,
                color: Colors.white,
              ),
            ),
          ),
          if (widget.isProcessing)
            Positioned(
              top: 0.0,
              left: 0.0,
              child: Container(
                width: screenWidth,
                height: photoHeight,
                color: const Color.fromRGBO(0, 0, 0, 0.3),
                child: const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _handleOnPhotoTap() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoViewerScreen(
          photoUrl: widget.photo.urlLarge,
          height: photoHeight,
        ),
      ),
    );
  }

  /// We do only *like* (no *unlike*) on double tap.
  /// This is what Instagram does as well.
  ///
  /// We do this to avoid the risk when the user frequently double taps.
  ///
  /// We could toggle like and unlike on double tap.
  /// But what if they double tap multiple times in a row?
  void _handleOnPhotoDoubleTap() {
    setState(() {
      _heartIconSize = 100.0;
    });

    _likePhoto();
    _animateLikeAction();

    // if (widget.photo.isLiked) {
    //   _animateUnlikeAction(context);
    //   _likePhoto();
    // } else {
    //   _animateLikeAction(context);
    //   _unlikePhoto();
    // }
  }

  void _animateLikeAction() {
    _animationController.reset();
    _animationController.forward().whenComplete(() {
      setState(() {
        _heartIconSize = 0.0;
      });
    });
  }

  Future<void> _likePhoto() async {
    final bool isLiked =
        ref.read(photoProvider(widget.photo.id))?.isLiked ??
        widget.photo.isLiked;

    // Only run if the user hasn't liked this photo.
    if (isLiked) return;

    // Set the `isLiked` to true state even though the request hasn't been made.
    ref.read(photoStoreProvider.notifier).setIsLiked(widget.photo.id, true);

    LikePhotoResponse response = await _likePhotoService.like(widget.photo.id);

    if (!response.success || response.data == null) {
      // Switch the `isLiked` state back to false if the status is failed.
      ref.read(photoStoreProvider.notifier).setIsLiked(widget.photo.id, false);
      return;
    }

    ref
        .read(photoStoreProvider.notifier)
        .setTotalLikes(widget.photo.id, response.data!.totalLikes);
  }
}
